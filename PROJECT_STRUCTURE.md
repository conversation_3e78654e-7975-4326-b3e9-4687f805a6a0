# Project Structure & Implementation Guide

## 📁 Monorepo Structure

```
crm-server/
├── apps/                           # Frontend applications
│   ├── student-portal/             # Student-facing Next.js app
│   ├── staff-portal/               # Staff-facing Next.js app
│   ├── admin-portal/               # Admin-facing Next.js app
│   └── api-gateway/                # Central API gateway
├── services/                       # Backend microservices
│   ├── lead-student-service/       # Lead capture & student management
│   ├── staff-teacher-service/      # Staff & teacher management
│   ├── admin-service/              # Admin operations (high security)
│   ├── course-group-service/       # Course & group management
│   ├── payment-records-service/    # Payment records (note-taking)
│   └── communication-service/      # Basic notifications & messaging
├── packages/                       # Shared packages
│   ├── ui/                         # Shared UI components
│   ├── database/                   # Database schemas & migrations
│   ├── auth/                       # Authentication utilities
│   ├── types/                      # TypeScript type definitions
│   ├── utils/                      # Shared utilities
│   └── config/                     # Shared configurations
├── tools/                          # Development tools
│   ├── eslint-config/              # ESLint configuration
│   ├── typescript-config/          # TypeScript configuration
│   └── tailwind-config/            # Tailwind configuration
├── docs/                           # Documentation
├── docker/                         # Docker configurations
├── scripts/                        # Build and deployment scripts
├── .github/                        # GitHub Actions workflows
├── package.json                    # Root package.json
├── turbo.json                      # Turborepo configuration
└── README.md
```

## 🛠️ Technology Specifications

### Package Manager & Monorepo
- **Package Manager**: pnpm (faster, more efficient)
- **Monorepo Tool**: Turborepo (optimized builds, caching)
- **Node Version**: 20.x LTS

### Frontend Stack (Each App)
```json
{
  "dependencies": {
    "next": "^15.3.4",
    "react": "^18.2.0",
    "react-dom": "^18.2.0",
    "typescript": "^5.0.0",
    "tailwindcss": "^3.3.0",
    "@tailwindcss/forms": "^0.5.0",
    "@tailwindcss/typography": "^0.5.0",
    "shadcn-ui": "latest",
    "@radix-ui/react-*": "latest",
    "lucide-react": "latest",
    "zustand": "^4.4.0",
    "@tanstack/react-query": "^5.0.0",
    "react-hook-form": "^7.45.0",
    "zod": "^3.22.0",
    "@hookform/resolvers": "^3.3.0",
    "date-fns": "^2.30.0",
    "clsx": "^2.0.0",
    "tailwind-merge": "^2.0.0"
  },
  "devDependencies": {
    "@types/node": "^20.0.0",
    "@types/react": "^18.2.0",
    "@types/react-dom": "^18.2.0",
    "eslint": "^8.50.0",
    "eslint-config-next": "^15.0.0",
    "prettier": "^3.0.0",
    "vitest": "^1.0.0",
    "@testing-library/react": "^14.0.0",
    "@testing-library/jest-dom": "^6.0.0"
  }
}
```

### Backend Stack (Each Service)
```json
{
  "dependencies": {
    "next": "^15.3.4",
    "typescript": "^5.0.0",
    "@prisma/client": "^5.5.0",
    "prisma": "^5.5.0",
    "zod": "^3.22.0",
    "bcryptjs": "^2.4.3",
    "jsonwebtoken": "^9.0.0",
    "nodemailer": "^6.9.0",
    "redis": "^4.6.0",
    "winston": "^3.10.0"
  },
  "devDependencies": {
    "@types/bcryptjs": "^2.4.0",
    "@types/jsonwebtoken": "^9.0.0",
    "@types/nodemailer": "^6.4.0",
    "jest": "^29.7.0",
    "@types/jest": "^29.5.0",
    "supertest": "^6.3.0"
  }
}
```

## 🔧 Development Setup Commands

### Initial Setup
```bash
# Clone and setup
git clone <repository-url> crm-server
cd crm-server

# Install dependencies
pnpm install

# Setup environment variables
cp .env.example .env.local

# Setup database
pnpm db:setup

# Start development
pnpm dev
```

### Development Scripts
```json
{
  "scripts": {
    "dev": "turbo run dev",
    "build": "turbo run build",
    "test": "turbo run test",
    "lint": "turbo run lint",
    "type-check": "turbo run type-check",
    "db:setup": "turbo run db:setup",
    "db:migrate": "turbo run db:migrate",
    "db:seed": "turbo run db:seed",
    "clean": "turbo run clean"
  }
}
```

## 🗄️ Core CRM Database Schema

### Essential Tables Structure

#### Users Table (Shared across services)
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  role user_role NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  avatar_url TEXT,
  is_active BOOLEAN DEFAULT true,
  email_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE user_role AS ENUM ('lead', 'student', 'teacher', 'admin');
```

#### Leads Table
```sql
CREATE TABLE leads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20),
  source VARCHAR(100), -- website, referral, social media, etc.
  status lead_status DEFAULT 'new',
  notes TEXT,
  assigned_to UUID REFERENCES users(id), -- assigned staff member
  converted_to_student_id UUID REFERENCES students(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE lead_status AS ENUM ('new', 'contacted', 'qualified', 'converted', 'lost');
```

#### Students Table
```sql
CREATE TABLE students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  student_id VARCHAR(20) UNIQUE NOT NULL,
  date_of_birth DATE,
  emergency_contact JSONB,
  enrollment_date DATE NOT NULL,
  current_level VARCHAR(50),
  status student_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE student_status AS ENUM ('active', 'inactive', 'graduated', 'suspended');
```

#### Teachers Table
```sql
CREATE TABLE teachers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES users(id),
  employee_id VARCHAR(20) UNIQUE NOT NULL,
  specialization VARCHAR(100),
  hire_date DATE NOT NULL,
  qualifications JSONB,
  kpi_score DECIMAL(5,2) DEFAULT 0.00,
  status teacher_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE teacher_status AS ENUM ('active', 'inactive', 'terminated');
```

#### Courses Table
```sql
CREATE TABLE courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_code VARCHAR(20) UNIQUE NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  level VARCHAR(50) NOT NULL,
  duration_weeks INTEGER NOT NULL,
  max_students INTEGER DEFAULT 20,
  price DECIMAL(10,2) NOT NULL,
  status course_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE course_status AS ENUM ('active', 'inactive', 'archived');
```

#### Groups Table
```sql
CREATE TABLE groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_name VARCHAR(100) NOT NULL,
  course_id UUID REFERENCES courses(id),
  teacher_id UUID REFERENCES teachers(id),
  cabinet_id UUID REFERENCES cabinets(id),
  max_students INTEGER DEFAULT 15,
  current_students INTEGER DEFAULT 0,
  start_date DATE NOT NULL,
  end_date DATE,
  schedule JSONB, -- days and times
  status group_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE group_status AS ENUM ('active', 'completed', 'cancelled');
```

#### Cabinets Table (Classrooms)
```sql
CREATE TABLE cabinets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cabinet_number VARCHAR(20) UNIQUE NOT NULL,
  cabinet_name VARCHAR(100),
  capacity INTEGER NOT NULL,
  equipment JSONB, -- projector, whiteboard, etc.
  location VARCHAR(100),
  status cabinet_status DEFAULT 'available',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE cabinet_status AS ENUM ('available', 'occupied', 'maintenance');
```

#### Payment Records Table
```sql
CREATE TABLE payment_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  amount DECIMAL(10,2) NOT NULL,
  payment_date DATE NOT NULL,
  payment_method VARCHAR(50), -- cash, card, transfer, etc.
  description TEXT,
  status payment_status DEFAULT 'recorded',
  notes TEXT,
  recorded_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE payment_status AS ENUM ('recorded', 'verified', 'disputed');
```

#### Student Assignments Table
```sql
CREATE TABLE student_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  teacher_id UUID REFERENCES teachers(id),
  group_id UUID REFERENCES groups(id),
  title VARCHAR(200) NOT NULL,
  description TEXT,
  due_date DATE,
  status assignment_status DEFAULT 'assigned',
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE assignment_status AS ENUM ('assigned', 'submitted', 'graded', 'overdue');
```

#### Student Resources Table
```sql
CREATE TABLE student_resources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(200) NOT NULL,
  type resource_type NOT NULL,
  content TEXT, -- for notes
  file_url TEXT, -- for books/documents
  course_id UUID REFERENCES courses(id),
  group_id UUID REFERENCES groups(id),
  is_public BOOLEAN DEFAULT false,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE resource_type AS ENUM ('note', 'book', 'document', 'link');
```

## 🔐 Authentication Flow

### JWT Token Structure
```typescript
interface JWTPayload {
  sub: string;           // User ID
  email: string;         // User email
  role: UserRole;        // User role
  permissions: string[]; // Specific permissions
  iat: number;          // Issued at
  exp: number;          // Expires at
}
```

### Role-Based Permissions
```typescript
const PERMISSIONS = {
  // Student permissions
  'student:read-own-profile': ['student'],
  'student:update-own-profile': ['student'],
  'student:view-courses': ['student', 'teacher', 'admin'],
  
  // Teacher permissions
  'teacher:manage-classes': ['teacher', 'admin'],
  'teacher:grade-students': ['teacher', 'admin'],
  'teacher:view-student-progress': ['teacher', 'admin'],
  
  // Admin permissions
  'admin:manage-users': ['admin'],
  'admin:system-config': ['admin'],
  'admin:view-analytics': ['admin'],
  'admin:manage-payments': ['admin']
} as const;
```

## 🚀 API Design Standards

### RESTful API Conventions
```typescript
// Resource naming
GET    /api/v1/students           // List students
GET    /api/v1/students/:id       // Get student by ID
POST   /api/v1/students           // Create student
PUT    /api/v1/students/:id       // Update student
DELETE /api/v1/students/:id       // Delete student

// Nested resources
GET    /api/v1/students/:id/courses     // Get student's courses
POST   /api/v1/students/:id/enrollments // Enroll student in course
```

### Response Format
```typescript
interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
  };
}
```

## 🧪 Testing Strategy

### Testing Pyramid
```
                    E2E Tests (10%)
                 ┌─────────────────┐
                 │   Playwright    │
                 └─────────────────┘
              Integration Tests (20%)
           ┌─────────────────────────┐
           │   API Testing (Jest)    │
           └─────────────────────────┘
         Unit Tests (70%)
    ┌─────────────────────────────────┐
    │  Component & Function Tests     │
    │        (Vitest/Jest)            │
    └─────────────────────────────────┘
```

### Test Configuration
```typescript
// vitest.config.ts
export default defineConfig({
  test: {
    environment: 'jsdom',
    setupFiles: ['./test/setup.ts'],
    globals: true,
  },
});

// jest.config.js (for API tests)
module.exports = {
  testEnvironment: 'node',
  setupFilesAfterEnv: ['<rootDir>/test/setup.js'],
  testMatch: ['**/__tests__/**/*.test.ts'],
};
```

## 📦 Deployment Configuration

### Vercel Configuration
```json
{
  "version": 2,
  "builds": [
    {
      "src": "apps/*/package.json",
      "use": "@vercel/next"
    }
  ],
  "routes": [
    {
      "src": "/api/students/(.*)",
      "dest": "/api/services/student-service/$1"
    },
    {
      "src": "/api/staff/(.*)",
      "dest": "/api/services/staff-service/$1"
    }
  ]
}
```

### Environment Variables
```bash
# Database
DATABASE_URL="postgresql://..."
REDIS_URL="redis://..."

# Authentication
NEXTAUTH_SECRET="..."
NEXTAUTH_URL="..."

# External Services
STRIPE_SECRET_KEY="..."
SENDGRID_API_KEY="..."
VERCEL_BLOB_READ_WRITE_TOKEN="..."

# Feature Flags
ENABLE_ANALYTICS="true"
ENABLE_PAYMENTS="true"
```

This structure provides a solid foundation for building a scalable, maintainable microservices architecture using modern technologies and best practices.
