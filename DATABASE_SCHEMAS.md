# Database Schemas - Distributed CRM Architecture

## 🗄️ Database Distribution Strategy

Each service has its own dedicated database to ensure complete data isolation and security. This document outlines the schema for each database.

## 🔐 Database Security Levels

- **Maximum Security**: VPN + IP Restricted (Admin, Payment, Auth)
- **High Security**: Role-based access with encryption (Staff)
- **Standard Security**: Encrypted with data protection (Student, Lead, Course, Communication)

---

## 1. Admin Database (Maximum Security)
**Connection**: `ADMIN_DATABASE_URL` (VPN + IP Restricted)
**Users**: Admin, Cashier, Accounting

```sql
-- Admin Users Table
CREATE TABLE admin_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  role admin_role NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  mfa_enabled BOOLEAN DEFAULT true,
  mfa_secret VARCHAR(255),
  last_login TIMESTAMP,
  ip_whitelist TEXT[], -- Allowed IP addresses
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE admin_role AS ENUM ('admin', 'cashier', 'accounting');

-- System Configuration
CREATE TABLE system_config (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  config_key VARCHAR(100) UNIQUE NOT NULL,
  config_value JSONB NOT NULL,
  description TEXT,
  updated_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Audit Logs (Comprehensive logging for admin actions)
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES admin_users(id),
  action VARCHAR(100) NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  resource_id VARCHAR(100),
  old_values JSONB,
  new_values JSONB,
  ip_address INET,
  user_agent TEXT,
  timestamp TIMESTAMP DEFAULT NOW()
);

-- Financial Reports
CREATE TABLE financial_reports (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  report_type VARCHAR(50) NOT NULL,
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  data JSONB NOT NULL,
  generated_by UUID REFERENCES admin_users(id),
  created_at TIMESTAMP DEFAULT NOW()
);
```

---

## 2. Staff Database (High Security)
**Connection**: `STAFF_DATABASE_URL`
**Users**: Reception, Managers, Test Checkers, Teachers

```sql
-- Staff Users Table
CREATE TABLE staff_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  role staff_role NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  employee_id VARCHAR(20) UNIQUE NOT NULL,
  department VARCHAR(100),
  hire_date DATE NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE staff_role AS ENUM ('reception', 'manager', 'test_checker', 'teacher');

-- Teachers (Extended staff information)
CREATE TABLE teachers (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  staff_user_id UUID REFERENCES staff_users(id),
  specialization VARCHAR(100),
  qualifications JSONB,
  kpi_score DECIMAL(5,2) DEFAULT 0.00,
  performance_metrics JSONB,
  status teacher_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE teacher_status AS ENUM ('active', 'inactive', 'on_leave');

-- Teacher KPIs
CREATE TABLE teacher_kpis (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  teacher_id UUID REFERENCES teachers(id),
  metric_name VARCHAR(100) NOT NULL,
  metric_value DECIMAL(10,2) NOT NULL,
  target_value DECIMAL(10,2),
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  calculated_at TIMESTAMP DEFAULT NOW()
);

-- Class Assignments
CREATE TABLE class_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  teacher_id UUID REFERENCES teachers(id),
  course_id UUID NOT NULL, -- Reference to course service
  group_id UUID NOT NULL, -- Reference to course service
  cabinet_id UUID, -- Reference to course service
  start_date DATE NOT NULL,
  end_date DATE,
  schedule JSONB, -- Days and times
  status assignment_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE assignment_status AS ENUM ('active', 'completed', 'cancelled');

-- Student Assignments (Created by teachers)
CREATE TABLE student_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  teacher_id UUID REFERENCES teachers(id),
  student_id UUID NOT NULL, -- Reference to student service
  group_id UUID NOT NULL, -- Reference to course service
  title VARCHAR(200) NOT NULL,
  description TEXT,
  due_date DATE,
  status assignment_status_student DEFAULT 'assigned',
  notes TEXT,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE assignment_status_student AS ENUM ('assigned', 'submitted', 'graded', 'overdue');
```

---

## 3. Student Database (Standard Security)
**Connection**: `STUDENT_DATABASE_URL`
**Users**: Students

```sql
-- Student Users Table
CREATE TABLE student_users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  phone VARCHAR(20),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Students (Extended student information)
CREATE TABLE students (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_user_id UUID REFERENCES student_users(id),
  student_id VARCHAR(20) UNIQUE NOT NULL,
  date_of_birth DATE,
  emergency_contact JSONB,
  enrollment_date DATE NOT NULL,
  current_level VARCHAR(50),
  status student_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE student_status AS ENUM ('active', 'inactive', 'graduated', 'suspended');

-- Student Enrollments
CREATE TABLE student_enrollments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  course_id UUID NOT NULL, -- Reference to course service
  group_id UUID NOT NULL, -- Reference to course service
  enrollment_date DATE NOT NULL,
  completion_date DATE,
  status enrollment_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE enrollment_status AS ENUM ('active', 'completed', 'dropped', 'transferred');

-- Student Progress
CREATE TABLE student_progress (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  assignment_id UUID NOT NULL, -- Reference to staff service
  progress_percentage DECIMAL(5,2) DEFAULT 0.00,
  grade VARCHAR(10),
  feedback TEXT,
  submitted_at TIMESTAMP,
  graded_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Student Resources Access
CREATE TABLE student_resource_access (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID REFERENCES students(id),
  resource_id UUID NOT NULL, -- Reference to course service
  accessed_at TIMESTAMP DEFAULT NOW(),
  access_duration INTEGER -- in minutes
);
```

---

## 4. Lead Database (Standard Security)
**Connection**: `LEAD_DATABASE_URL`
**Users**: Reception, Managers

```sql
-- Leads Table
CREATE TABLE leads (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(20),
  source lead_source NOT NULL,
  status lead_status DEFAULT 'new',
  notes TEXT,
  assigned_to UUID, -- Reference to staff service
  converted_to_student_id UUID, -- Reference to student service
  conversion_date DATE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE lead_source AS ENUM ('website', 'referral', 'social_media', 'walk_in', 'phone', 'advertisement');
CREATE TYPE lead_status AS ENUM ('new', 'contacted', 'qualified', 'converted', 'lost');

-- Lead Activities
CREATE TABLE lead_activities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lead_id UUID REFERENCES leads(id),
  activity_type activity_type NOT NULL,
  description TEXT,
  performed_by UUID, -- Reference to staff service
  scheduled_at TIMESTAMP,
  completed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE activity_type AS ENUM ('call', 'email', 'meeting', 'follow_up', 'assessment');

-- Lead Sources Tracking
CREATE TABLE lead_sources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  source_name VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  conversion_rate DECIMAL(5,2) DEFAULT 0.00,
  total_leads INTEGER DEFAULT 0,
  converted_leads INTEGER DEFAULT 0,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

---

## 5. Course Database (Standard Security)
**Connection**: `COURSE_DATABASE_URL`
**Users**: Admin, Managers, Teachers

```sql
-- Courses Table
CREATE TABLE courses (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_code VARCHAR(20) UNIQUE NOT NULL,
  title VARCHAR(200) NOT NULL,
  description TEXT,
  level VARCHAR(50) NOT NULL,
  duration_weeks INTEGER NOT NULL,
  max_students INTEGER DEFAULT 20,
  price DECIMAL(10,2) NOT NULL,
  status course_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE course_status AS ENUM ('active', 'inactive', 'archived');

-- Groups Table
CREATE TABLE groups (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  group_name VARCHAR(100) NOT NULL,
  course_id UUID REFERENCES courses(id),
  teacher_id UUID, -- Reference to staff service
  cabinet_id UUID REFERENCES cabinets(id),
  max_students INTEGER DEFAULT 15,
  current_students INTEGER DEFAULT 0,
  start_date DATE NOT NULL,
  end_date DATE,
  schedule JSONB, -- days and times
  status group_status DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE group_status AS ENUM ('active', 'completed', 'cancelled');

-- Cabinets (Classrooms)
CREATE TABLE cabinets (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  cabinet_number VARCHAR(20) UNIQUE NOT NULL,
  cabinet_name VARCHAR(100),
  capacity INTEGER NOT NULL,
  equipment JSONB, -- projector, whiteboard, etc.
  location VARCHAR(100),
  status cabinet_status DEFAULT 'available',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE cabinet_status AS ENUM ('available', 'occupied', 'maintenance');

-- Student Resources
CREATE TABLE student_resources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(200) NOT NULL,
  type resource_type NOT NULL,
  content TEXT, -- for notes
  file_url TEXT, -- for books/documents
  course_id UUID REFERENCES courses(id),
  group_id UUID REFERENCES groups(id),
  is_public BOOLEAN DEFAULT false,
  created_by UUID, -- Reference to staff service
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE resource_type AS ENUM ('note', 'book', 'document', 'link');
```

---

## 6. Payment Database (Maximum Security)
**Connection**: `PAYMENT_DATABASE_URL` (VPN + IP Restricted)
**Users**: Admin, Cashier, Accounting

```sql
-- Payment Records Table
CREATE TABLE payment_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  student_id UUID NOT NULL, -- Reference to student service
  amount DECIMAL(10,2) NOT NULL,
  payment_date DATE NOT NULL,
  payment_method payment_method NOT NULL,
  description TEXT,
  status payment_status DEFAULT 'recorded',
  notes TEXT,
  recorded_by UUID, -- Reference to admin service
  verified_by UUID, -- Reference to admin service
  verification_date DATE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE payment_method AS ENUM ('cash', 'card', 'bank_transfer', 'check', 'online');
CREATE TYPE payment_status AS ENUM ('recorded', 'verified', 'disputed', 'refunded');

-- Fee Structures
CREATE TABLE fee_structures (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  course_id UUID NOT NULL, -- Reference to course service
  fee_type fee_type NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency VARCHAR(3) DEFAULT 'USD',
  effective_date DATE NOT NULL,
  expiry_date DATE,
  is_active BOOLEAN DEFAULT true,
  created_by UUID, -- Reference to admin service
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE fee_type AS ENUM ('tuition', 'registration', 'material', 'exam', 'late_payment');

-- Financial Transactions (Audit trail for all financial activities)
CREATE TABLE financial_transactions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  payment_record_id UUID REFERENCES payment_records(id),
  transaction_type transaction_type NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  description TEXT,
  performed_by UUID, -- Reference to admin service
  ip_address INET,
  timestamp TIMESTAMP DEFAULT NOW()
);

CREATE TYPE transaction_type AS ENUM ('payment', 'refund', 'adjustment', 'fee_waiver');
```

---

## 7. Communication Database (Standard Security)
**Connection**: `COMMUNICATION_DATABASE_URL`
**Users**: All services (internal communication)

```sql
-- Service Events (Inter-service communication)
CREATE TABLE service_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type VARCHAR(100) NOT NULL,
  source_service VARCHAR(50) NOT NULL,
  target_service VARCHAR(50),
  payload JSONB NOT NULL,
  status event_status DEFAULT 'pending',
  processed_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE event_status AS ENUM ('pending', 'processed', 'failed', 'retrying');

-- Messages (User-to-user communication)
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  sender_id UUID NOT NULL,
  sender_service VARCHAR(50) NOT NULL,
  recipient_id UUID NOT NULL,
  recipient_service VARCHAR(50) NOT NULL,
  subject VARCHAR(200),
  content TEXT NOT NULL,
  message_type message_type DEFAULT 'direct',
  status message_status DEFAULT 'sent',
  read_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE message_type AS ENUM ('direct', 'announcement', 'notification');
CREATE TYPE message_status AS ENUM ('sent', 'delivered', 'read', 'failed');

-- Notifications
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL,
  user_service VARCHAR(50) NOT NULL,
  title VARCHAR(200) NOT NULL,
  content TEXT NOT NULL,
  type notification_type NOT NULL,
  priority notification_priority DEFAULT 'normal',
  read BOOLEAN DEFAULT false,
  action_url TEXT,
  expires_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE notification_type AS ENUM ('info', 'warning', 'error', 'success');
CREATE TYPE notification_priority AS ENUM ('low', 'normal', 'high', 'urgent');
```

---

## 8. Auth Database (Maximum Security)
**Connection**: `AUTH_DATABASE_URL` (VPN + IP Restricted)
**Users**: All (authentication)

```sql
-- User Credentials (Centralized authentication)
CREATE TABLE user_credentials (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  service_name VARCHAR(50) NOT NULL,
  service_user_id UUID NOT NULL,
  role VARCHAR(50) NOT NULL,
  mfa_enabled BOOLEAN DEFAULT false,
  mfa_secret VARCHAR(255),
  last_login TIMESTAMP,
  failed_login_attempts INTEGER DEFAULT 0,
  locked_until TIMESTAMP,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Sessions
CREATE TABLE user_sessions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES user_credentials(id),
  session_token VARCHAR(255) UNIQUE NOT NULL,
  ip_address INET,
  user_agent TEXT,
  expires_at TIMESTAMP NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
);

-- Permissions
CREATE TABLE user_permissions (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES user_credentials(id),
  permission VARCHAR(100) NOT NULL,
  resource VARCHAR(100),
  granted_by UUID REFERENCES user_credentials(id),
  granted_at TIMESTAMP DEFAULT NOW(),
  expires_at TIMESTAMP
);

-- MFA Tokens
CREATE TABLE mfa_tokens (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES user_credentials(id),
  token_hash VARCHAR(255) NOT NULL,
  token_type mfa_type NOT NULL,
  expires_at TIMESTAMP NOT NULL,
  used_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW()
);

CREATE TYPE mfa_type AS ENUM ('totp', 'sms', 'email', 'backup');
```

This distributed database architecture ensures complete data isolation while maintaining the ability for services to communicate securely through well-defined APIs and event systems.
