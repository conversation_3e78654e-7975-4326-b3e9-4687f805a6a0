# Repository Setup Guide - Distributed CRM Architecture

## 🗂️ Repository Structure Overview

Each microservice will be in its own repository with individual databases and deployment pipelines. This ensures complete separation of concerns and maximum security.

## 📋 Repository List

### **1. crm-admin-service** (High Security - VPN + IP Restricted)
**Purpose**: Admin, Cashier, Accounting operations
**Database**: `ADMIN_DATABASE_URL`
**Security Level**: Maximum
**Users**: Admin, Cashier, Accounting

### **2. crm-staff-service** (Moderate Security)
**Purpose**: Reception, Managers, Test Checkers, Teachers
**Database**: `STAFF_DATABASE_URL`
**Security Level**: High
**Users**: Reception, Managers, Test Checkers, Teachers

### **3. crm-student-service** (Standard Security)
**Purpose**: Student portal and academic information
**Database**: `STUDENT_DATABASE_URL`
**Security Level**: Standard
**Users**: Students

### **4. crm-lead-service**
**Purpose**: Lead capture and conversion management
**Database**: `LEAD_DATABASE_URL`
**Security Level**: Standard
**Users**: Reception, Managers

### **5. crm-course-service**
**Purpose**: Course catalog and group management
**Database**: `COURSE_DATABASE_URL`
**Security Level**: Standard
**Users**: Admin, Managers, Teachers

### **6. crm-payment-service** (High Security - VPN + IP Restricted)
**Purpose**: Payment records and financial data
**Database**: `PAYMENT_DATABASE_URL`
**Security Level**: Maximum
**Users**: Admin, Cashier, Accounting

### **7. crm-communication-service**
**Purpose**: Inter-service communication and events
**Database**: `COMMUNICATION_DATABASE_URL`
**Security Level**: Standard
**Users**: All services (internal)

### **8. crm-auth-service** (High Security - VPN + IP Restricted)
**Purpose**: Centralized authentication and authorization
**Database**: `AUTH_DATABASE_URL`
**Security Level**: Maximum
**Users**: All (authentication)

### **9. crm-api-gateway**
**Purpose**: Request routing and load balancing
**Database**: None (routing only)
**Security Level**: High
**Users**: All (routing)

### **10. crm-shared-types**
**Purpose**: Shared TypeScript types and interfaces
**Database**: None
**Security Level**: Standard
**Users**: All services (development)

## 🔧 Individual Repository Setup

### **Standard Repository Structure** (Each Service)
```
crm-[service-name]/
├── src/
│   ├── app/                    # Next.js 15.3.4 App Router
│   │   ├── api/               # API routes
│   │   ├── (auth)/            # Auth-protected routes
│   │   ├── globals.css        # Global styles
│   │   └── layout.tsx         # Root layout
│   ├── components/            # React components
│   │   ├── ui/               # Shadcn/ui components
│   │   └── forms/            # Form components
│   ├── lib/                  # Utility functions
│   │   ├── db.ts             # Database connection
│   │   ├── auth.ts           # Authentication
│   │   └── utils.ts          # Helper functions
│   ├── types/                # TypeScript types
│   └── middleware.ts         # Next.js middleware
├── prisma/
│   ├── schema.prisma         # Database schema
│   ├── migrations/           # Database migrations
│   └── seed.ts              # Seed data
├── public/                   # Static assets
├── tests/                    # Test files
├── .env.example             # Environment variables template
├── .env.local               # Local environment variables
├── package.json             # Dependencies
├── next.config.js           # Next.js configuration
├── tailwind.config.js       # Tailwind CSS configuration
├── tsconfig.json            # TypeScript configuration
├── vercel.json              # Vercel deployment configuration
└── README.md                # Service documentation
```

## 🗄️ Database Connection Configuration

### **Environment Variables Template**
```bash
# .env.example (for each service)

# Database Connection (Service-specific)
DATABASE_URL="postgresql://username:password@host:port/database_name"

# Service URLs (for inter-service communication)
API_GATEWAY_URL="https://crm-api-gateway.vercel.app"
AUTH_SERVICE_URL="https://crm-auth-service.vercel.app"
ADMIN_SERVICE_URL="https://crm-admin-service.vercel.app"
STAFF_SERVICE_URL="https://crm-staff-service.vercel.app"
STUDENT_SERVICE_URL="https://crm-student-service.vercel.app"
LEAD_SERVICE_URL="https://crm-lead-service.vercel.app"
COURSE_SERVICE_URL="https://crm-course-service.vercel.app"
PAYMENT_SERVICE_URL="https://crm-payment-service.vercel.app"
COMMUNICATION_SERVICE_URL="https://crm-communication-service.vercel.app"

# Authentication
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="https://your-service-url.vercel.app"
JWT_SECRET="your-jwt-secret"

# Service-to-Service Authentication
SERVICE_API_KEY="your-service-api-key"
SERVICE_NAME="crm-[service-name]"

# Security Configuration (for high-security services)
VPN_REQUIRED=true
IP_WHITELIST="10.0.0.0/8,***********/24"
MFA_REQUIRED=true
SESSION_TIMEOUT=15

# External Services
REDIS_URL="redis://your-redis-url"
EMAIL_SERVICE_URL="your-email-service"
```

## 🔐 Security Configuration by Service

### **High Security Services** (Admin, Payment, Auth)
```typescript
// lib/security.ts
export const SECURITY_CONFIG = {
  vpnRequired: process.env.VPN_REQUIRED === 'true',
  ipWhitelist: process.env.IP_WHITELIST?.split(',') || [],
  mfaRequired: process.env.MFA_REQUIRED === 'true',
  sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '15'),
  auditLogging: 'comprehensive',
  dataEncryption: 'AES-256',
  accessLogging: true,
  rateLimiting: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  }
};
```

### **Standard Security Services** (Staff, Student, etc.)
```typescript
// lib/security.ts
export const SECURITY_CONFIG = {
  sessionTimeout: parseInt(process.env.SESSION_TIMEOUT || '60'),
  auditLogging: 'standard',
  dataEncryption: 'AES-256',
  accessLogging: true,
  rateLimiting: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 1000 // limit each IP to 1000 requests per windowMs
  }
};
```

## 🚀 Deployment Configuration

### **Vercel Configuration** (vercel.json)
```json
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "env": {
    "DATABASE_URL": "@database-url",
    "NEXTAUTH_SECRET": "@nextauth-secret",
    "SERVICE_API_KEY": "@service-api-key"
  },
  "functions": {
    "src/app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        }
      ]
    }
  ]
}
```

## 🔄 Inter-Service Communication Setup

### **Service Registry** (crm-shared-types/src/services.ts)
```typescript
export const SERVICE_REGISTRY = {
  ADMIN: process.env.ADMIN_SERVICE_URL || 'https://crm-admin-service.vercel.app',
  STAFF: process.env.STAFF_SERVICE_URL || 'https://crm-staff-service.vercel.app',
  STUDENT: process.env.STUDENT_SERVICE_URL || 'https://crm-student-service.vercel.app',
  LEAD: process.env.LEAD_SERVICE_URL || 'https://crm-lead-service.vercel.app',
  COURSE: process.env.COURSE_SERVICE_URL || 'https://crm-course-service.vercel.app',
  PAYMENT: process.env.PAYMENT_SERVICE_URL || 'https://crm-payment-service.vercel.app',
  COMMUNICATION: process.env.COMMUNICATION_SERVICE_URL || 'https://crm-communication-service.vercel.app',
  AUTH: process.env.AUTH_SERVICE_URL || 'https://crm-auth-service.vercel.app'
} as const;
```

### **Service Client** (crm-shared-types/src/client.ts)
```typescript
export class ServiceClient {
  private baseURL: string;
  private apiKey: string;
  private serviceName: string;

  constructor(serviceUrl: string) {
    this.baseURL = serviceUrl;
    this.apiKey = process.env.SERVICE_API_KEY!;
    this.serviceName = process.env.SERVICE_NAME!;
  }

  async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': this.apiKey,
        'X-Service-Name': this.serviceName,
        ...options.headers,
      },
    });

    if (!response.ok) {
      throw new Error(`Service request failed: ${response.statusText}`);
    }

    return response.json();
  }
}
```

## 📋 Development Workflow

### **1. Repository Creation**
```bash
# Create each repository
git clone https://github.com/your-org/crm-admin-service.git
git clone https://github.com/your-org/crm-staff-service.git
git clone https://github.com/your-org/crm-student-service.git
# ... etc for each service
```

### **2. Local Development Setup**
```bash
# For each service
cd crm-[service-name]
pnpm install
cp .env.example .env.local
# Configure your database connection strings
pnpm db:migrate
pnpm db:seed
pnpm dev
```

### **3. Database Setup**
```bash
# Each service manages its own database
pnpm prisma generate
pnpm prisma db push
pnpm prisma db seed
```

### **4. Deployment**
```bash
# Deploy each service individually
vercel --prod --project crm-admin-service
vercel --prod --project crm-staff-service
# ... etc
```

This setup ensures complete separation of services while maintaining secure communication between them. Each service can be developed, tested, and deployed independently while sharing common types and utilities.
