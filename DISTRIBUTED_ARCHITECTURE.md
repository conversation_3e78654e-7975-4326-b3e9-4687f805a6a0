# Distributed Microservices Architecture - Innovative Centre CRM

## 🏗️ Distributed System Overview

**Architecture Type**: True Microservices with Separate Repositories & Databases
**Communication**: REST APIs + Event-Driven Architecture
**Security**: VPN + IP Restrictions for Admin Services
**Deployment**: Individual Vercel deployments per service

## 📊 User Role Structure

### **Admin Services** (High Security - VPN + IP Restrictions)
- **Admin**: System administration, user management, system configuration
- **Cashier**: Payment processing, financial transactions, invoice management
- **Accounting**: Financial reporting, audit trails, payment verification

### **Staff Services** (Moderate Security)
- **Reception**: Lead capture, initial student contact, appointment scheduling
- **Managers**: Staff oversight, performance monitoring, business operations
- **Test Checkers**: Student assessments, progress evaluation, grade management
- **Teachers**: Class management, assignment creation, student instruction

### **Student Services** (Standard Security)
- **Students**: Academic portal, assignment viewing, resource access, progress tracking

## 🗂️ Repository Structure

```
innovative-centre-crm/
├── crm-admin-service/              # Admin/Cashier/Accounting
├── crm-staff-service/              # Reception/Managers/Test Checkers/Teachers
├── crm-student-service/            # Students
├── crm-lead-service/               # Lead capture and management
├── crm-course-service/             # Course and group management
├── crm-payment-service/            # Payment records and financial data
├── crm-communication-service/      # Inter-service messaging
├── crm-auth-service/               # Centralized authentication
├── crm-api-gateway/                # API routing and load balancing
└── crm-shared-types/               # Shared TypeScript types
```

## 🗄️ Database Distribution

### **Admin Database** (PostgreSQL - High Security)
**Connection**: `ADMIN_DATABASE_URL` (VPN + IP Restricted)
```sql
-- Tables: users (admin roles), audit_logs, system_config, financial_reports
-- Security: Encrypted at rest, VPN access only, IP whitelist
```

### **Staff Database** (PostgreSQL - Moderate Security)
**Connection**: `STAFF_DATABASE_URL`
```sql
-- Tables: staff_users, teachers, managers, reception_staff, test_checkers
-- Security: Encrypted at rest, role-based access
```

### **Student Database** (PostgreSQL - Standard Security)
**Connection**: `STUDENT_DATABASE_URL`
```sql
-- Tables: students, student_profiles, academic_records, assignments
-- Security: Standard encryption, student data protection
```

### **Lead Database** (PostgreSQL)
**Connection**: `LEAD_DATABASE_URL`
```sql
-- Tables: leads, lead_sources, conversion_tracking
-- Security: Standard encryption, GDPR compliant
```

### **Course Database** (PostgreSQL)
**Connection**: `COURSE_DATABASE_URL`
```sql
-- Tables: courses, groups, cabinets, schedules, enrollments
-- Security: Standard encryption, academic data protection
```

### **Payment Database** (PostgreSQL - High Security)
**Connection**: `PAYMENT_DATABASE_URL` (VPN + IP Restricted)
```sql
-- Tables: payment_records, financial_transactions, fee_structures
-- Security: Encrypted at rest, VPN access only, audit logging
```

### **Communication Database** (PostgreSQL)
**Connection**: `COMMUNICATION_DATABASE_URL`
```sql
-- Tables: messages, notifications, event_logs, service_communications
-- Security: Standard encryption, message privacy
```

### **Auth Database** (PostgreSQL - High Security)
**Connection**: `AUTH_DATABASE_URL` (VPN + IP Restricted)
```sql
-- Tables: user_credentials, sessions, permissions, mfa_tokens
-- Security: Encrypted at rest, VPN access only, session management
```

## 🔐 Security Architecture

### **Admin Service Security (Paramount)**
```typescript
// VPN + IP Restrictions
const ADMIN_SECURITY = {
  vpnRequired: true,
  ipWhitelist: ['10.0.0.0/8', '***********/24'], // Your VPN IPs
  mfaRequired: true,
  sessionTimeout: 15, // minutes
  auditLogging: 'comprehensive',
  dataEncryption: 'AES-256',
  accessLogging: true
};
```

### **Staff Service Security**
```typescript
const STAFF_SECURITY = {
  roleBasedAccess: true,
  sessionTimeout: 60, // minutes
  auditLogging: 'standard',
  dataEncryption: 'AES-256',
  accessLogging: true
};
```

### **Student Service Security**
```typescript
const STUDENT_SECURITY = {
  dataProtection: 'GDPR',
  sessionTimeout: 120, // minutes
  auditLogging: 'basic',
  dataEncryption: 'AES-256'
};
```

## 🔄 Inter-Service Communication

### **API Gateway Configuration**
```typescript
// crm-api-gateway/routes.ts
const SERVICE_ROUTES = {
  '/api/admin/*': 'https://crm-admin-service.vercel.app',
  '/api/staff/*': 'https://crm-staff-service.vercel.app',
  '/api/students/*': 'https://crm-student-service.vercel.app',
  '/api/leads/*': 'https://crm-lead-service.vercel.app',
  '/api/courses/*': 'https://crm-course-service.vercel.app',
  '/api/payments/*': 'https://crm-payment-service.vercel.app',
  '/api/auth/*': 'https://crm-auth-service.vercel.app'
};
```

### **Service-to-Service Authentication**
```typescript
// Shared JWT tokens for service communication
interface ServiceToken {
  service: string;
  permissions: string[];
  iat: number;
  exp: number;
}

// Example: Staff service calling Student service
const callStudentService = async (studentId: string) => {
  const serviceToken = generateServiceToken('staff-service');
  const response = await fetch(`${STUDENT_SERVICE_URL}/api/students/${studentId}`, {
    headers: {
      'Authorization': `Bearer ${serviceToken}`,
      'X-Service-Name': 'staff-service'
    }
  });
};
```

### **Event-Driven Communication**
```typescript
// Event types for inter-service communication
interface ServiceEvents {
  'student.enrolled': { studentId: string; courseId: string; groupId: string };
  'payment.recorded': { studentId: string; amount: number; paymentId: string };
  'teacher.assigned': { teacherId: string; groupId: string };
  'lead.converted': { leadId: string; studentId: string };
}

// Event publishing
const publishEvent = async (eventType: keyof ServiceEvents, data: any) => {
  await fetch(`${COMMUNICATION_SERVICE_URL}/api/events`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ type: eventType, data, timestamp: new Date() })
  });
};
```

## 📋 Service Specifications

### **1. CRM Admin Service** (crm-admin-service)
**Repository**: `innovative-centre-crm/crm-admin-service`
**Database**: `ADMIN_DATABASE_URL` (VPN + IP Restricted)
**Users**: Admin, Cashier, Accounting
**Security**: Maximum (VPN + IP + MFA)

**Features**:
- System administration and configuration
- User role management across all services
- Financial oversight and reporting
- Audit trail management
- System health monitoring

### **2. CRM Staff Service** (crm-staff-service)
**Repository**: `innovative-centre-crm/crm-staff-service`
**Database**: `STAFF_DATABASE_URL`
**Users**: Reception, Managers, Test Checkers, Teachers
**Security**: High (Role-based access)

**Features**:
- Staff profile management
- Teacher KPI tracking
- Class and assignment management
- Student assessment and grading
- Performance monitoring

### **3. CRM Student Service** (crm-student-service)
**Repository**: `innovative-centre-crm/crm-student-service`
**Database**: `STUDENT_DATABASE_URL`
**Users**: Students
**Security**: Standard (Data protection)

**Features**:
- Student portal and dashboard
- Assignment and resource access
- Progress tracking
- Class schedule viewing
- Academic record access

### **4. CRM Lead Service** (crm-lead-service)
**Repository**: `innovative-centre-crm/crm-lead-service`
**Database**: `LEAD_DATABASE_URL`
**Users**: Reception, Managers
**Security**: Standard

**Features**:
- Lead capture and management
- Lead assignment and tracking
- Conversion workflow
- Source tracking and analytics

### **5. CRM Course Service** (crm-course-service)
**Repository**: `innovative-centre-crm/crm-course-service`
**Database**: `COURSE_DATABASE_URL`
**Users**: Admin, Managers, Teachers
**Security**: Standard

**Features**:
- Course catalog management
- Group creation and management
- Cabinet assignment
- Schedule management
- Enrollment tracking

### **6. CRM Payment Service** (crm-payment-service)
**Repository**: `innovative-centre-crm/crm-payment-service`
**Database**: `PAYMENT_DATABASE_URL` (VPN + IP Restricted)
**Users**: Admin, Cashier, Accounting
**Security**: Maximum (VPN + IP + Audit)

**Features**:
- Payment record management
- Financial transaction tracking
- Fee structure management
- Payment verification workflow
- Financial reporting

### **7. CRM Communication Service** (crm-communication-service)
**Repository**: `innovative-centre-crm/crm-communication-service`
**Database**: `COMMUNICATION_DATABASE_URL`
**Users**: All (for inter-service communication)
**Security**: Standard

**Features**:
- Inter-service messaging
- Event publishing and subscription
- Notification management
- Communication logging

### **8. CRM Auth Service** (crm-auth-service)
**Repository**: `innovative-centre-crm/crm-auth-service`
**Database**: `AUTH_DATABASE_URL` (VPN + IP Restricted)
**Users**: All (authentication)
**Security**: Maximum (VPN + IP + MFA)

**Features**:
- Centralized authentication
- JWT token management
- Multi-factor authentication
- Session management
- Permission validation

### **9. CRM API Gateway** (crm-api-gateway)
**Repository**: `innovative-centre-crm/crm-api-gateway`
**Database**: None (routing only)
**Users**: All (routing)
**Security**: Request validation and routing

**Features**:
- Request routing to appropriate services
- Load balancing
- Rate limiting
- Request/response logging
- Security validation

## 🚀 Deployment Strategy

### **Individual Service Deployment**
```bash
# Each service deployed separately on Vercel
vercel --prod --project crm-admin-service
vercel --prod --project crm-staff-service
vercel --prod --project crm-student-service
# ... etc for each service
```

### **Environment Variables per Service**
```bash
# crm-admin-service/.env.production
ADMIN_DATABASE_URL="postgresql://admin:<EMAIL>:5432/admin_db"
VPN_REQUIRED=true
IP_WHITELIST="10.0.0.0/8,***********/24"
MFA_REQUIRED=true

# crm-staff-service/.env.production
STAFF_DATABASE_URL="postgresql://staff:<EMAIL>:5432/staff_db"
ADMIN_SERVICE_URL="https://crm-admin-service.vercel.app"

# crm-student-service/.env.production
STUDENT_DATABASE_URL="postgresql://student:<EMAIL>:5432/student_db"
STAFF_SERVICE_URL="https://crm-staff-service.vercel.app"
```

This distributed architecture ensures maximum security for your financial data while maintaining scalability and separation of concerns across all services.
